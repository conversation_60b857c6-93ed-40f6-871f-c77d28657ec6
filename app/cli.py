import typer
from typing import Optional, List, Dict
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
from rich.live import Live
from concurrent.futures import ThreadPoolExecutor
import logging
from .database.setup import setup_database
from .models.controls import load_controls, search_controls
from .models.mitre_data import load_mitre_attack_json, load_nist_to_mitre_mappings_csv, search_mitre_techniques
from .services.llm import get_llm_analysis
from .database.connection import get_db_connection

# Initialize logger for this module
logger = logging.getLogger(__name__)

app = typer.Typer(
    help="""
    Security Controls Analysis CLI

    This tool helps you manage and query a database of security controls
    using semantic search. It uses pgvector to store and search embeddings 
    of security controls from the SCF framework.

    Setup:
    1. Make sure PostgreSQL with pgvector extension is running
    2. Create a .env file with your database configuration
    3. Run 'init-db' to initialize the database
    4. Use 'query' to search for controls based on threat scenarios
    """
)

console = Console()

def display_controls_table(controls: list) -> Table:
    """Display controls in a Rich table."""
    table = Table(title="Top Matching Security Controls", show_lines=True)
    table.add_column("Number", style="cyan", no_wrap=True)
    table.add_column("Title", style="magenta", min_width=30) # Allow wrapping for title
    table.add_column("Domain", style="green")
    # table.add_column("Description", style="white", overflow="fold", min_width=50, max_width=80) # Keep description concise or omit
    table.add_column("Weight", style="yellow")
    table.add_column("Relevance", style="blue")
    # table.add_column("NIST 800-53", style="dim") # Optionally add if needed

    for number, title, domain, description, weight, similarity, nist_references in controls: # Unpack 7 values
        table.add_row(
            number,
            Text(title, overflow="fold"), # Ensure title wraps
            domain,
            # Text(description, overflow="fold"), # Display full description with wrapping
            str(weight),
            f"{similarity:.4f}" # Format similarity
            # str(nist_references) if nist_references else "N/A" # Display NIST references
        )
    return table

@app.command()
def init_db():
    """
    Initialize the database and load controls, MITRE data, and mappings.
    """
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            overall_task = progress.add_task("[yellow]Initializing database...", total=4)

            progress.update(overall_task, description="[blue]1/4 Setting up base schema...")
            setup_database()
            progress.advance(overall_task)

            conn = get_db_connection()
            try:
                # progress.update(overall_task, description="[blue]2/4 Loading SCF controls...")
                # total_scf, loaded_scf = load_controls()
                # logger.info(f"SCF Controls: {loaded_scf}/{total_scf} loaded.")
                # progress.advance(overall_task)

                progress.update(overall_task, description="[blue]3/4 Loading MITRE ATT&CK techniques...")
                loaded_mitre = load_mitre_attack_json(conn)
                logger.info(f"MITRE Techniques: {loaded_mitre} loaded.")
                progress.advance(overall_task)

                # progress.update(overall_task, description="[blue]4/4 Loading NIST to MITRE mappings...")
                # loaded_mappings = load_nist_to_mitre_mappings_csv(conn)
                # logger.info(f"NIST-MITRE Mappings: {loaded_mappings} processed.")
                # progress.advance(overall_task)
            finally:
                if conn:
                    conn.close()

            progress.update(overall_task, description=f"[green]✓ Database initialized successfully!", completed=True)
            console.print(f"Summary: MITRE Tech={loaded_mitre}")

    except Exception as e:
        logger.error(f"Error during init_db: {e}", exc_info=True)
        console.print(f"[red]Error during database initialization: {e}")
        raise

@app.command()
def query(
    threat_scenario: str = typer.Argument(..., help="The threat scenario to search for matching controls"),
    limit: Optional[int] = typer.Option(5, "--limit", "-l", help="Number of top matching SCF controls to display and top MITRE techniques to search."),
    scf_llm_context_limit: Optional[int] = typer.Option(10, "--scf-llm-limit", help="Total number of SCF controls to fetch for LLM analysis context (includes top matches)."),
    analyze: bool = typer.Option(True, "--analyze/--no-analyze", "-a/ ", help="Whether to analyze results with LLM for mitigation suggestions")
):
    """
    Search for security controls and MITRE ATT&CK techniques matching a threat scenario.
    
    Examples:
        main.py query "unauthorized access to sensitive data"
        main.py query "social engineering attack" -l 10 --scf-llm-limit 15
        main.py query "data breach through phishing" --no-analyze
    """
    if scf_llm_context_limit < limit:
        console.print(f"[yellow]Warning: SCF LLM context limit ({scf_llm_context_limit}) is less than display limit ({limit}). Adjusting SCF LLM context limit to {limit}.[/yellow]")
        scf_llm_context_limit = limit

    scf_controls_for_llm: List[tuple] = []
    mitre_results: List[dict] = []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        # Run searches sequentially
        search_task_scf = progress.add_task("[cyan]Searching SCF controls...", total=1)
        # Fetch a potentially larger list of SCF controls for the LLM
        scf_controls_for_llm = search_controls(threat_scenario, scf_llm_context_limit)
        progress.update(search_task_scf, completed=1, description="[green]✓ SCF controls retrieved.")

        search_task_mitre = progress.add_task("[cyan]Searching MITRE techniques...", total=1)
        # MITRE search uses the standard 'limit' for display, but let's see more for diagnostics if specific query
        diagnostic_mitre_limit = limit
        if threat_scenario.lower() == "s3 using aws imdsv1":
            diagnostic_mitre_limit = 20 # Fetch more for this specific diagnostic case
            console.print(f"[bold magenta]Diagnostic: Fetching top {diagnostic_mitre_limit} MITRE techniques for '{threat_scenario}'[/bold magenta]")

        mitre_results_full = search_mitre_techniques(threat_scenario, diagnostic_mitre_limit)
        
        if threat_scenario.lower() == "s3 using aws imdsv1":
            console.print("[bold magenta]Diagnostic: Full MITRE search results:[/bold magenta]")
            diag_table = Table(title="MITRE Diagnostic Full Results")
            diag_table.add_column("Rank", style="dim")
            diag_table.add_column("ATT&CK ID", style="red")
            diag_table.add_column("Name")
            diag_table.add_column("Similarity", style="blue")
            for i, tech in enumerate(mitre_results_full):
                diag_table.add_row(str(i+1), tech.get('attack_id', 'N/A'), tech.get('name', 'N/A'), f"{tech.get('similarity', -1):.4f}")
            console.print(diag_table)
        
        # Use the original 'limit' for what's processed further and displayed in the normal table
        mitre_results = mitre_results_full[:limit] 

        progress.update(search_task_mitre, completed=1, description="[green]✓ MITRE techniques retrieved.")
    
    # Display only the top 'limit' SCF controls in the table
    scf_controls_to_display = scf_controls_for_llm[:limit]

    if scf_controls_to_display:
        table = display_controls_table(scf_controls_to_display)
        console.print(table)
    else:
        console.print("[yellow]No matching SCF controls found to display.")

    if mitre_results:
        console.print("\n[bold yellow]Matching MITRE ATT&CK Techniques (Direct Search):[/bold yellow]")
        mitre_table = Table(title="Directly Matched MITRE ATT&CK Techniques", show_lines=True)
        mitre_table.add_column("ATT&CK ID", style="red")
        mitre_table.add_column("Name", style="white")
        mitre_table.add_column("Similarity", style="blue")
        for tech in mitre_results:
            mitre_table.add_row(tech['attack_id'], tech['name'], f"{tech.get('similarity', 'N/A'):.4f}")
        console.print(mitre_table)
    else:
        console.print("[yellow]No directly matching MITRE ATT&CK techniques found via vector search.")
    
    if analyze and (scf_controls_for_llm or mitre_results): # Check if LLM has any SCF controls to work with
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            analysis_task = progress.add_task("[yellow]Analyzing results...", total=None)
            # Pass the full fetched list of SCF controls to the LLM analysis function
            analysis = get_llm_analysis(threat_scenario, scf_controls_for_llm, directly_searched_mitre_techniques=mitre_results)
            progress.update(analysis_task, description="[green]✓ Analysis complete", completed=True)
            
        console.print("\n[bold blue]Analysis Results:[/bold blue]")
        console.print("=" * console.width)
        console.print(Markdown(analysis))
        console.print("=" * console.width)
    elif analyze:
        console.print("[yellow]Skipping analysis as no relevant SCF controls or MITRE techniques were found.") 